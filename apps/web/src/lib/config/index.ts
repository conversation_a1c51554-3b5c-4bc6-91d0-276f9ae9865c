export const APP_ENV = process.env.APP_ENV;

export const WEBSITE_BASE_URL =
  process.env.NEXT_PUBLIC_WEBSITE_BASE_URL &&
  process.env.NEXT_PUBLIC_WEBSITE_BASE_URL.trim() !== ''
    ? process.env.NEXT_PUBLIC_WEBSITE_BASE_URL
    : 'https://beta.taostats.io';

export const WEBSITE_API_URL =
  process.env.WEBSITE_API_URL && process.env.WEBSITE_API_URL.trim() !== ''
    ? process.env.WEBSITE_API_URL
    : 'https://api-dev-v2.taostats.io/api';

export const WEBSITE_API_TOKEN =
  process.env.WEBSITE_API_TOKEN && process.env.WEBSITE_API_TOKEN.trim() !== ''
    ? process.env.WEBSITE_API_TOKEN
    : 'rZob3GNGpTnDrrdkOWz3Zbh1EbV1UVBud7enbsLAxwga0BKyJw0PmLCtAA5yiD5w';

// React Query configuration constants
export const REACT_QUERY_CONFIG = {
  DEFAULT_STALE_TIME: 12 * 1000, // 12 seconds
  DEFAULT_REFETCH_INTERVAL: 12 * 1000, // 12 seconds
  DEFAULT_GC_TIME: 12 * 1000, // 12 seconds
  DAILY_STALE_TIME: 24 * 60 * 60 * 1000, // 24 hours
  DAILY_REFETCH_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
  MINUTE_STALE_TIME: 60 * 1000, // 60 seconds
} as const;
