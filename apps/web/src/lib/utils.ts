/* eslint-disable @typescript-eslint/no-explicit-any -- TODO: using `any` here is legacy from the website, fix later */

export function constructURL(
  baseURL: string,
  params: Record<string, any>
): string {
  const queryString = new URLSearchParams(
    objectToQueryParams(params)
  ).toString();
  return queryString.length > 0 ? `${baseURL}?${queryString}` : baseURL;
}

function objectToQueryParams(obj: Record<string, any>): Record<string, string> {
  return Object.entries(obj)
    .filter(([_, value]) => value !== undefined && value !== null)
    .reduce<Record<string, string>>((acc, [key, value]) => {
      acc[key] = String(value);
      return acc;
    }, {});
}

export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
}
